{% extends "base.html" %}

{% block title %}Process Image - Timeline Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-cog me-2"></i>Process Image</h1>
    <a href="{{ url_for('timeline.view', id=image.timeline_id) }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to Timeline
    </a>
</div>

<div class="row">
    <div class="col-md-5">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Image Preview</h4>
            </div>
            <div class="card-body text-center">
                <img src="{{ url_for('static', filename='uploads/' + image.filename) }}"
                     class="img-fluid rounded" alt="{{ image.original_filename }}">
                <div class="mt-3">
                    <h5>{{ image.original_filename }}</h5>
                    <p class="text-muted">
                        Uploaded on {{ image.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-7">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">Process Image</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <i class="fas fa-robot me-2"></i> The AI has analyzed this image and generated content. You can edit this content and add a time mark.
                </div>

                <form method="post" action="{{ url_for('image.process', id=image.id) }}">
                    {{ form.hidden_tag() }}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            {{ form.ai_content.label(class="form-label mb-0") }}
                            <button type="button" id="generateAiContentBtn" class="btn btn-sm btn-success">
                                <i class="fas fa-robot me-1"></i> AI Gen Content
                            </button>
                        </div>
                        {{ form.ai_content(class="form-control", rows=8, placeholder="AI-generated content about this image", id="ai-content-textarea") }}
                        {% for error in form.ai_content.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        <!-- Enhanced Loading UI -->
                        <div id="ai-loading" class="mt-3" style="display: none;">
                            <div class="card border-success">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="spinner-border spinner-border-sm text-success me-3" role="status">
                                            <span class="visually-hidden">Processing...</span>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">AI Analysis in Progress</h6>
                                            <small id="status-message" class="text-muted">Initializing...</small>
                                        </div>
                                        <span id="progress-percentage" class="badge bg-success">0%</span>
                                    </div>

                                    <!-- Progress Bar -->
                                    <div class="progress mb-2" style="height: 8px;">
                                        <div id="progress-bar" class="progress-bar bg-success progress-bar-striped progress-bar-animated"
                                             role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                        </div>
                                    </div>

                                    <!-- Estimated Time -->
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <span id="elapsed-time">0s</span> elapsed
                                        </small>
                                        <small class="text-muted">
                                            Est. <span id="estimated-time">~5s</span> remaining
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-4">
                        {{ form.time_mark.label(class="form-label") }}
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                            {{ form.time_mark(class="form-control", type="time") }}
                        </div>
                        <small class="text-muted">The time when this screenshot was taken</small>
                        {% for error in form.time_mark.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-info btn-lg") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const generateBtn = document.getElementById('generateAiContentBtn');
    const textarea = document.getElementById('ai-content-textarea');
    const loadingDiv = document.getElementById('ai-loading');
    const statusMessage = document.getElementById('status-message');
    const progressBar = document.getElementById('progress-bar');
    const progressPercentage = document.getElementById('progress-percentage');
    const elapsedTime = document.getElementById('elapsed-time');
    const estimatedTime = document.getElementById('estimated-time');

    let currentJobId = null;
    let pollingInterval = null;
    let startTime = null;
    let elapsedTimer = null;

    generateBtn.addEventListener('click', function() {
        startAIGeneration();
    });

    function startAIGeneration() {
        // Disable button and show loading
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Starting...';
        loadingDiv.style.display = 'block';

        // Reset progress
        updateProgress(0, 'Initializing AI analysis...');
        startTime = Date.now();
        startElapsedTimer();

        // Start AI generation
        fetch('{{ url_for("image.generate_ai_content", id=image.id) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentJobId = data.job_id;
                generateBtn.innerHTML = '<i class="fas fa-cog fa-spin me-1"></i> Processing...';

                // Start polling for status updates
                startPolling();
            } else {
                handleError(data.error || 'Failed to start AI content generation');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            handleError('An error occurred while starting AI content generation');
        });
    }

    function startPolling() {
        if (!currentJobId) return;

        pollingInterval = setInterval(() => {
            checkJobStatus();
        }, 1000); // Poll every second

        // Initial check
        checkJobStatus();
    }

    function checkJobStatus() {
        if (!currentJobId) return;

        fetch(`/image/ai-job/${currentJobId}/status`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const job = data.job;
                updateProgress(job.progress, job.status_message);

                if (job.status === 'completed') {
                    handleSuccess(job.result_content);
                } else if (job.status === 'failed') {
                    handleError(job.error_message || 'AI processing failed');
                } else if (job.status === 'timeout') {
                    handleError('AI processing timed out');
                }
            } else {
                handleError(data.error || 'Failed to get job status');
            }
        })
        .catch(error => {
            console.error('Polling error:', error);
            // Continue polling on network errors
        });
    }

    function updateProgress(progress, message) {
        progressBar.style.width = progress + '%';
        progressBar.setAttribute('aria-valuenow', progress);
        progressPercentage.textContent = progress + '%';
        statusMessage.textContent = message;

        // Update estimated time
        if (progress > 0 && startTime) {
            const elapsed = (Date.now() - startTime) / 1000;
            const estimatedTotal = (elapsed / progress) * 100;
            const remaining = Math.max(0, estimatedTotal - elapsed);
            estimatedTime.textContent = `~${Math.ceil(remaining)}s`;
        }
    }

    function startElapsedTimer() {
        elapsedTimer = setInterval(() => {
            if (startTime) {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                elapsedTime.textContent = elapsed + 's';
            }
        }, 1000);
    }

    function stopTimers() {
        if (pollingInterval) {
            clearInterval(pollingInterval);
            pollingInterval = null;
        }
        if (elapsedTimer) {
            clearInterval(elapsedTimer);
            elapsedTimer = null;
        }
    }

    function handleSuccess(content) {
        stopTimers();

        // Update textarea with generated content
        textarea.value = content;

        // Show success state
        updateProgress(100, 'AI analysis completed successfully!');

        // Show success message
        showAlert('AI content generated successfully! The analysis has been added to the content field.', 'success');

        // Reset button after a short delay
        setTimeout(() => {
            resetButton();
            loadingDiv.style.display = 'none';
        }, 2000);
    }

    function handleError(errorMessage) {
        stopTimers();

        // Show error message
        showAlert(errorMessage, 'danger');

        // Reset immediately on error
        resetButton();
        loadingDiv.style.display = 'none';
    }

    function resetButton() {
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<i class="fas fa-robot me-1"></i> AI Gen Content';
        currentJobId = null;
        startTime = null;
    }

    function showAlert(message, type) {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert alert at the top of the card body
        const cardBody = document.querySelector('.card-body');
        cardBody.insertBefore(alertDiv, cardBody.firstChild);

        // Auto-dismiss after 8 seconds for success, 10 seconds for errors
        const dismissTime = type === 'success' ? 8000 : 10000;
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, dismissTime);
    }

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        stopTimers();
    });
});
</script>
{% endblock %}
